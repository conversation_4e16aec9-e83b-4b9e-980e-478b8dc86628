import React, { useEffect, useRef, useState, useCallback } from "react";
import { RegularNotificationIcon } from "../../../../../public/assets/Svgs";
import {
  CaretDown,
  Checks,
  PencilSimple,
  WarningCircle,
} from "@phosphor-icons/react";
import { fetcher, formatDate, formatTime } from "@/utils/axios";
import { mutate } from "swr";
import endpoints from "@/utils/endpoints";
import Image from "next/image";
import { updateStatus } from "@/services/payment.service";
import { toast } from "react-hot-toast";

const markAs = [
  "Still pending",
  "Paid Delay",
  "Paid on time",
  "Free Cancellation",
  "Paid Cancellation",
];

const optionClasses: { [key: string]: string } = {
  "Still pending": "text-[#FF2727]",
  "Paid Delay": "text-[#D90BCB]",
  "Paid on time": "text-[#007D14]",
  "Free Cancellation": "text-[#FF6108]",
  "Paid Cancellation": "text-[#008EA1]",
};

interface PaymentTBodyProps {
  TableData: Array<unknown>;
  setIsReminderModal: (value: boolean) => void;
  isReminderModal: boolean;
  setIsCancelSessionModal: (value: boolean) => void;
  isCancelSessionModal: boolean;
  setIsUpdatePayment: (value: boolean) => void;
  isUpdatePayment: boolean;
  setSinglePaymentData: (item: Item) => void;
  paymentLoading: boolean;
  query: string;
  mutatePaymentTrackerData?: () => void; // Add this line
  setCancellationChargeID: (id: string) => void;
  setPaymentID: (id: string) => void;
  urlClientId: string;
  setIsSessionAmountUpdate?: (value: boolean) => void;
  setPendingStatus?: (status: string | null) => void;
  therapistData?: {
    minFee?: number;
    maxFee?: number;
  };
  onUpdateSelectedOptionRef?: (updateFn: (itemId: string, option: string) => void) => void;
}

export interface Item {
  _id: string;
  clientId?: { name: string; email: string };
  sessionDate?: string;
  amount?: { value: string };
  paymentDate?: string;
  status?: string;
  sessionStatus?: string;
  mutatePaymentTrackerData?: () => void; // Add this line
  cancellationFee?: {
    value?: string;
  };
  publicCalendarAmountUpdated?: boolean;
  fromPublicCalender?: boolean;
  minFee?: number;
  maxFee?: number;
}

const PaymentTBody: React.FC<PaymentTBodyProps> = ({
  TableData,
  setIsReminderModal,
  isReminderModal,
  setIsCancelSessionModal,
  isCancelSessionModal,
  setIsUpdatePayment,
  isUpdatePayment,
  setSinglePaymentData,
  paymentLoading,
  query,
  mutatePaymentTrackerData, // Destructure it here
  setCancellationChargeID,
  setPaymentID,
  urlClientId,
  setIsSessionAmountUpdate,
  setPendingStatus,
  therapistData,
  onUpdateSelectedOptionRef,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<{
    [key: number]: string;
  }>({});
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(
    null
  );

  const dropdownRef = useRef<HTMLDivElement[]>([]);

  const toggleDropdown = (index: number) => {
    setOpenDropdownIndex(openDropdownIndex === index ? null : index);
  };

  // Function to update selected option by itemId
  const updateSelectedOptionByItemId = useCallback((itemId: string, option: string) => {
    // Find the index of the item with the given itemId
    const itemIndex = (TableData as Item[]).findIndex(item => item._id === itemId);
    if (itemIndex !== -1) {
      setSelectedOptions((prev) => ({ ...prev, [itemIndex]: option }));
    }
  }, [TableData]);

  // Expose the update function to parent component
  useEffect(() => {
    if (onUpdateSelectedOptionRef) {
      onUpdateSelectedOptionRef(updateSelectedOptionByItemId);
    }
  }, [onUpdateSelectedOptionRef, updateSelectedOptionByItemId]);

  // Detect click outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        openDropdownIndex !== null &&
        !dropdownRef.current[openDropdownIndex]?.contains(event.target as Node)
      ) {
        setOpenDropdownIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdownIndex]);

  const handleSelect = (index: number, option: string, itemId: string, item: Item) => {
    setOpenDropdownIndex(null);

    if (option === "Paid Cancellation") {
      setSelectedOptions((prev) => ({ ...prev, [index]: option }));
      setIsCancelSessionModal(!isCancelSessionModal);
      setCancellationChargeID(itemId);
    } else if ((option === "Paid Delay" || option === "Paid on time") &&
               item.minFee && item.maxFee &&
               (item.publicCalendarAmountUpdated === undefined || item.publicCalendarAmountUpdated === false)) {
      // Open update payment modal for sessions with fee range that haven't been updated
      // Don't update selectedOptions here - it will be updated after amount is set
      setIsUpdatePayment(!isUpdatePayment);
      setPaymentID(itemId);
      setIsSessionAmountUpdate?.(true);
      setPendingStatus?.(option);
      // Don't update status here - it will be updated after amount is set
      return;
    } else {
      setSelectedOptions((prev) => ({ ...prev, [index]: option }));
      updateStatus(itemId, option).then(() => {
        toast.success("Status updated successfully!");
      }).catch(() => {
        // Error toast is already handled in the service
      });
    }

    const url = `${endpoints.paymentTracker.getPaymentsByTherapist}?${query}`;

    mutate(url, async () => {
      await fetcher(url);

      if (mutatePaymentTrackerData) {
        await mutatePaymentTrackerData();
      }
    });
  };

  useEffect(() => {
    setSelectedOptions(
      TableData.reduce((acc: { [key: number]: string }, item, index) => {
        acc[index] = (item as Item).status || "Still pending";
        return acc;
      }, {})
    );
  }, [TableData]);

  const renderSkeletonRow = (key: number) => (
    <tr key={key}>
      <td className="p-15px">
        <div className="flex items-center gap-3 animate-pulse">
          <div className="w-[34px] h-[34px] rounded-full bg-gray-200"></div>
          <div>
            <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </td>
      {Array(5)
        .fill("")
        .map((_, idx) => (
          <td key={idx} className="p-15px">
            <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
          </td>
        ))}
      <td className="p-15px">
        <div className="flex gap-5">
          <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
        </div>
      </td>
    </tr>
  );

  return (
    <tbody className="divide-y divide-primary/10">
      {paymentLoading ? (
        Array(5)
          .fill("")
          .map((_, index) => renderSkeletonRow(index)) // Render 5 skeleton rows
      ) : TableData.length > 0 ? (
        (TableData as Item[]).map((item, index) => (
          <tr key={item._id || index}>
            <td className="p-15px">
              <div className="flex items-center gap-3">
                <div className="w-[34px] h-[34px] rounded-full border border-primary/20 bg-[#F5F5F7] overflow-hidden flex items-center justify-center">
                  <span className="text-xs_18 text-[#72748D] font-medium uppercase">
                    {item?.clientId?.name
                      ?.split(" ")
                      .map((n) => n.charAt(0))
                      .join("")}
                  </span>
                </div>
                <div>
                  <p className="text-sm/5 font-semibold text-primary capitalize">
                    {item?.clientId?.name}
                  </p>
                  <p className="text-xs_18 text-primary/70 font-medium">
                    {item?.clientId?.email}
                  </p>
                </div>
              </div>
            </td>
            <td className="p-15px text-primary/70 text-sm/5 font-medium whitespace-nowrap">
              {item?.sessionDate ? formatDate(item?.sessionDate) : "-"} -{" "}
              {item?.sessionDate ? formatTime(item?.sessionDate) : "-"}
            </td>
            <td className="p-15px">
              <span
                className={`inline-block py-1.5 px-3 font-semibold  rounded-full text-xs capitalize ${
                  item.sessionStatus &&
                  item.sessionStatus.trim() === "completed"
                    ? "bg-green-200 text-green-500"
                    : item.sessionStatus &&
                      ["Upcoming", "confirmed"].includes(
                        item.sessionStatus.trim()
                      )
                    ? "bg-orange-200 text-orange-600"
                    : item.sessionStatus &&
                      item.sessionStatus.trim() === "cancelled"
                    ? "bg-[#FFEDED] text-[#FF5959]"
                    : item.sessionStatus &&
                      item.sessionStatus.trim() === "rescheduled"
                    ? "bg-yellow-600/10 text-yellow-600"
                    : ""
                }`}
              >
                {item.sessionStatus}
              </span>
            </td>
            <td className="p-15px text-primary text-sm/5 font-semibold whitespace-nowrap">
              {item?.cancellationFee?.value &&
              ["Paid Cancellation"].includes(selectedOptions[index]) ? (
                <p
                  className={` ${optionClasses[selectedOptions[index]] || ""}`}
                >
                  ₹ {item?.cancellationFee?.value}
                </p>
              ) : (
                ""
              )}

              <p
                className={` flex items-center gap-2 ${
                  optionClasses[selectedOptions[index]] || ""
                } ${
                  ["Paid Cancellation"].includes(selectedOptions[index])
                    ? "!text-primary/30"
                    : ""
                }`}
              >
                {item.minFee && item.maxFee &&
                 (item.publicCalendarAmountUpdated === undefined || item.publicCalendarAmountUpdated === false)
                  ? `₹ ${item.minFee} - ₹ ${item.maxFee}`
                  : `₹ ${item?.amount?.value}`}{" "}
                {!urlClientId &&
                  ["Still pending"].includes(selectedOptions[index]) && (
                    <span className="cursor-pointer relative group !text-primary">
                      <WarningCircle size={16} />

                      <span className="absolute top-0 left-0 invisible group-hover:visible sm:whitespace-nowrap whitespace-normal w-64 sm:w-auto inline-block px-2 py-1 bg-primary/80 text-white text-xs z-10 rounded">
                        you can edit a client&apos;s payment amount from the
                        Client&apos;s Tab
                      </span>
                    </span>
                  )}
                {urlClientId &&
                  ["Still pending"].includes(selectedOptions[index]) && (
                    <span
                      className="cursor-pointer !text-primary"
                      onClick={() => {
                        setIsUpdatePayment?.(!isUpdatePayment);
                        if (item._id) {
                          setPaymentID(item._id);
                        }
                      }}
                    >
                      <PencilSimple size={14} />
                    </span>
                  )}
              </p>
            </td>

            <td className="p-15px relative">
              <div
                className="reletive"
                ref={(el) => {
                  dropdownRef.current[index] = el!;
                }}
              >
                <div
                  className="flex items-center gap-3 justify-between text-primary cursor-pointer"
                  onClick={() => toggleDropdown(index)}
                >
                  <p
                    className={`text-sm/7 font-medium capitalize whitespace-nowrap ${
                      optionClasses[selectedOptions[index]] || ""
                    }`}
                  >
                    {selectedOptions[index] || "Select"}
                  </p>
                  <CaretDown
                    size={20}
                    className={`transition-all duration-500 ${
                      openDropdownIndex === index ? "rotate-180" : ""
                    }`}
                  />
                </div>
                <ul
                  className={`absolute w-full left-0 rounded-base bg-white shadow-[0px_4px_12px_0px_#00000014] z-10 overflow-hidden transition-all duration-500 ${
                    index > 1 ? "bottom-12" : ""
                  } ${openDropdownIndex === index ? "h-[210px]" : "h-0"} `}
                >
                  {markAs.map((option) => {
                    return (
                      <li
                        key={option}
                        className={`p-2.5 text-xs_18 cursor-pointer text-primary hover:bg-green-600/10 ${
                          selectedOptions[index] === option
                            ? "bg-green-600/20 text-green-600 font-medium"
                            : ""
                        }`}
                        onClick={() => handleSelect(index, option, item._id, item)}
                      >
                        {option}
                      </li>
                    );
                  })}
                </ul>
              </div>
            </td>
            <td className="p-15px text-primary/70 text-sm/5 font-medium">
              {item?.paymentDate ? formatDate(item?.paymentDate) : "-"}
            </td>
            <td className="p-15px">
              <div className="flex items-center gap-5">
                {!["Paid on time", "Paid Delay"].includes(
                  item?.status ?? ""
                ) ? (
                  <RegularNotificationIcon
                    className="w-5 h-5 cursor-pointer"
                    pathFillColor="#242424"
                    strokeWidth="1.5"
                    onClick={() => {
                      setIsReminderModal(!isReminderModal);
                      setSinglePaymentData(item);
                    }}
                  />
                ) : (
                  <Checks size={26} className="text-yellow-600" />
                )}
              </div>
            </td>
          </tr>
        ))
      ) : (
        <tr>
          <td colSpan={8} className="p-15px text-center text-primary/70">
            <div className="flex flex-col items-center justify-center pt-16 pb-20">
              <Image
                width={1000}
                height={1000}
                src="/assets/images/dashboard/not-found-payment-list.webp"
                alt="no-data"
                className="w-[172px] h-auto"
              />
              <p className="text-xl/6 font-semibold pt-3.5 text-primary">
                No Payment Records Yet
              </p>
            </div>
          </td>
        </tr>
      )}
    </tbody>
  );
};

export default PaymentTBody;
