"use client";
import React, { useState, useEffect, useRef } from "react";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import Image from "next/image";
import {
  submitTherapistOnboarding,
  updateTherapistProfile,
  fetchTherapistProfile,
  TherapistOnboardingData
} from "@/services/public-calendar.service";
import { useFetchTherapistData } from "@/context/TherapistContext";
import { tabs, tabRedirects } from "../tabs/tabsData";
import { useRouter } from "next/navigation";
import { isTabCompleted, markTabAsCompleted } from "@/utils/completedTabs";
import CheckboxMultiSelect from "@/components/common/CheckboxMultiSelect";
import Loader from "@/components/common/Loader";
import TransitionLoader from "@/components/common/TransitionLoader";
import AnimatedButton from "@/components/common/AnimatedButton";

// Define the form data interface
interface FormData {
  name: string;
  pronouns: string;
  yearsOfExperience: string;
  gender: string;
  designation: string;
  therapyTypes: string[];
  minFee: string;
  maxFee: string;
  location: string;
  slotTypes: string[];
  qualifications: string;
  values: string[];
  concerns: string[];
  therapyApproach: string;
  profilePhoto: File | null;
  languages: string[];
}

// Define the error interface
interface FormErrors {
  [key: string]: string;
}

const PublicCalendar = () => {
  const [activeTab, setActiveTab] = useState("Setup Onboarding Process");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(0);
  const router = useRouter();

  // Create refs for form fields
  const nameRef = useRef<HTMLInputElement>(null);
  const pronounsRef = useRef<HTMLSelectElement>(null);
  const yearsOfExperienceRef = useRef<HTMLInputElement>(null);
  const genderRef = useRef<HTMLSelectElement>(null);
  const designationRef = useRef<HTMLSelectElement>(null);
  const therapyTypesRef = useRef<HTMLDivElement>(null);
  const minFeeRef = useRef<HTMLInputElement>(null);
  const maxFeeRef = useRef<HTMLInputElement>(null);
  const locationRef = useRef<HTMLSelectElement>(null);
  const slotTypesRef = useRef<HTMLDivElement>(null);
  const qualificationsRef = useRef<HTMLTextAreaElement>(null);
  const valuesRef = useRef<HTMLDivElement>(null);
  const concernsRef = useRef<HTMLDivElement>(null);
  const therapyApproachRef = useRef<HTMLTextAreaElement>(null);
  const profilePhotoRef = useRef<HTMLDivElement>(null);
  const languagesRef = useRef<HTMLDivElement>(null);

  // Get therapist data from context
  const { therapistData } = useFetchTherapistData() || { therapistData: null };

  // Mock options for dropdowns
  const options = {
    pronounOptions: ["She/Her", "He/Him", "They/Them", "She/They", "He/They"],
    genderOptions: ["Cisgender Male", "Cisgender Female", "Transgender", "Non-Binary", "Prefer not to say"],
    designationOptions: ["Counselling Psychologist", "Clinical Psychologist & Psychotherapist", "Psychotherapist"],
    therapyTypeOptions: ["Individual Psychotherapy", "Couple Psychotherapy"],
    slotTypeOptions: ["Introductory Call - 15min", "Consultation session with slot of 45mins, 50mins, 60mins, 75mins and 90mins"],
    valueOptions: ["Trauma Informed Lens", "Intersectional Lens", "Queer Affirmative Lens", "Neurodivergent Accessible", "Feminist Lens", "Anti-Caste Lens", "Disability Affirmative"],
    concernOptions: ["Work related stress and burnout ", "Depression", "Anxiety", "Relationships with friends, family, romantic partners", "Navigating Queerness", "Trauma experiences", "Navigating grief", "Body image concerns"],
    languageOptions: ["English", "Hindi", "Marathi", "Gujarati", "Assamese", "Bengali", "Odiya", "Punjabi"],
    locationOptions: ["Online Only", "Offline Only", "Hybrid(Online & Offline)"]
  };

  // Initialize form data
  const [formData, setFormData] = useState<FormData>({
    name: "",
    pronouns: "",
    yearsOfExperience: "",
    gender: "",
    designation: "",
    therapyTypes: [],
    minFee: "",
    maxFee: "",
    location: "Online Only",
    slotTypes: [],
    qualifications: "M.A in Counselling Psychology, Tata Institute of Social Sciences ; MPhil in Clinical Psychology, National Institute of Mental Health and Sciences",
    values: [],
    concerns: [],
    therapyApproach: "In our sessions, we'll first spend some time understanding what's bringing you to therapy and what you hope to get out of it. Then, we'll slowly explore the deeper reasons behind those concerns.\nIn therapy, understanding yourself better is powerful—and it's often the first step toward real change.",
    profilePhoto: null,
    languages: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  useEffect(() => {
    const getTherapistProfile = async () => {
      if (!therapistData?._id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetchTherapistProfile();

        // Check if we have data in the expected format
        if (response && response.status === "success" && Array.isArray(response.data)) {

          // Check if we have actual data to display
          if (response.data.length > 0) {
            const profileData = response.data[0]; // Get the first item in the array

            // Set edit mode to true if we have existing data
            setIsEditMode(true);
            let processedLanguages: string[] = [];
            if (Array.isArray(profileData.languages)) {
              processedLanguages = profileData.languages;
            } else if (profileData.languages) {
              if (typeof profileData.languages === 'string') {
                processedLanguages = [profileData.languages];
              } else {
                console.warn('Languages is not an array or string:', profileData.languages);
                processedLanguages = [];
              }
            } else {
              console.warn('Languages is undefined or null');
              processedLanguages = [];
            }

            // Helper function to safely convert to string
            const safeString = (value: string | Record<string, unknown> | undefined | null): string => {
              if (typeof value === 'string') {
                return value;
              }
              return "";
            };

            // Map API response to form data structure
            setFormData({
              name: safeString(profileData.name),
              pronouns: safeString(profileData.pronouns),
              yearsOfExperience: profileData.yearsOfExperience?.toString() || "",
              gender: safeString(profileData.gender),
              designation: safeString(profileData.designation),
              therapyTypes: Array.isArray(profileData.therapyTypes) ? profileData.therapyTypes : [],
              minFee: profileData.minFee?.toString() || "",
              maxFee: profileData.maxFee?.toString() || "",
              location: profileData.location && Array.isArray(profileData.location) && profileData.location.length > 0
                ? profileData.location[0]
                : "Online Only",
              slotTypes: Array.isArray(profileData.slotType) ? profileData.slotType : [],
              qualifications: safeString(profileData.professionalQualification) || "M.A in Counselling Psychology, Tata Institute of Social Sciences ; MPhil in Clinical Psychology, National Institute of Mental Health and Sciences",
              values: Array.isArray(profileData.values) ? profileData.values : [],
              concerns: Array.isArray(profileData.concerns) ? profileData.concerns : [],
              therapyApproach: safeString(profileData.practiceApproach) || "In our sessions, we'll first spend some time understanding what's bringing you to therapy and what you hope to get out of it. Then, we'll slowly explore the deeper reasons behind those concerns.\nIn therapy, understanding yourself better is powerful—and it's often the first step toward real change.",
              profilePhoto: null,
              languages: processedLanguages,
            });

            if (profileData.profilePicUrl) {
              if (typeof profileData.profilePicUrl === 'string') {
                setPreviewImage(profileData.profilePicUrl);
              } else {
                console.error("Profile image URL is not a string:", profileData.profilePicUrl);
              }
            } else {
              console.error("Profile image URL not found in API response");
            }
          }
        }
      } catch (error) {
        console.error("Error fetching therapist profile:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getTherapistProfile();
  }, [therapistData]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    if (name === 'yearsOfExperience') {
      const numericValue = value.replace(/\D/g, '');

      setFormData({
        ...formData,
        [name]: numericValue,
      });

      if (errors[name]) {
        setErrors({
          ...errors,
          [name]: "",
        });
      }

      return;
    }

    // For all other fields
    setFormData({
      ...formData,
      [name]: value,
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  // Multi-select handling is now done directly in the CheckboxMultiSelect component

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      const fileType = file.type;
      if (!['image/jpeg', 'image/jpg', 'image/png'].includes(fileType)) {
        return;
      }

      setFormData({
        ...formData,
        profilePhoto: file,
      });

      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewImage(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Clear error
      if (errors.profilePhoto) {
        setErrors({
          ...errors,
          profilePhoto: "",
        });
      }
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: FormErrors = {};

    // Check all required fields
    if (!formData.name) newErrors.name = "Name is required";
    if (!formData.pronouns) newErrors.pronouns = "Pronouns are required";

    // Validate years of experience
    if (!formData.yearsOfExperience) {
      newErrors.yearsOfExperience = "Years of experience is required";
    } else if (!/^\d+$/.test(formData.yearsOfExperience)) {
      newErrors.yearsOfExperience = "Years of experience must be a number";
    } else {
      const years = parseInt(formData.yearsOfExperience, 10);
      if (years < 0 || years > 50) {
        newErrors.yearsOfExperience = "Years of experience must be between 0 and 50";
      }
    }

    if (!formData.gender) newErrors.gender = "Gender is required";
    if (!formData.designation)
      newErrors.designation = "Designation is required";
    if (!Array.isArray(formData.therapyTypes) || formData.therapyTypes.length === 0)
      newErrors.therapyTypes = "At least one therapy type is required";
    // Validate minimum fee
    if (!formData.minFee) {
      newErrors.minFee = "Minimum fee is required";
    } else if (!/^\d+$/.test(formData.minFee)) {
      newErrors.minFee = "Minimum fee must be a number";
    } else {
      const minFee = parseInt(formData.minFee, 10);
      if (minFee < 1000) {
        newErrors.minFee = "Minimum fee must be at least 1000 INR";
      } else if (minFee > 10000) {
        newErrors.minFee = "Minimum fee cannot exceed 10,000 INR";
      }
    }

    // Validate maximum fee
    if (!formData.maxFee) {
      newErrors.maxFee = "Maximum fee is required";
    } else if (!/^\d+$/.test(formData.maxFee)) {
      newErrors.maxFee = "Maximum fee must be a number";
    } else {
      const maxFee = parseInt(formData.maxFee, 10);
      if (maxFee < 1000) {
        newErrors.maxFee = "Maximum fee must be at least 1000 INR";
      } else if (maxFee > 10000) {
        newErrors.maxFee = "Maximum fee cannot exceed 10,000 INR";
      }
    }

    // Validate that max fee is greater than or equal to min fee
    if (formData.minFee && formData.maxFee && !newErrors.minFee && !newErrors.maxFee) {
      const minFee = parseInt(formData.minFee, 10);
      const maxFee = parseInt(formData.maxFee, 10);
      if (maxFee < minFee) {
        newErrors.maxFee = "Maximum fee must be greater than or equal to minimum fee";
      }
    }
    if (!formData.location) newErrors.location = "Location is required";
    if (!Array.isArray(formData.slotTypes) || formData.slotTypes.length === 0)
      newErrors.slotTypes = "At least one slot type is required";
    if (!formData.qualifications)
      newErrors.qualifications = "Qualifications are required";
    if (!Array.isArray(formData.values) || formData.values.length === 0)
      newErrors.values = "At least one value is required";
    else if (formData.values.length > 3)
      newErrors.values = "Maximum 3 values can be selected";
    if (!Array.isArray(formData.concerns) || formData.concerns.length === 0)
      newErrors.concerns = "At least one concern is required";
    if (!formData.therapyApproach)
      newErrors.therapyApproach = "Therapy approach description is required";
    if (!formData.profilePhoto && !previewImage)
      newErrors.profilePhoto = "Profile photo is required";
    if (!Array.isArray(formData.languages) || formData.languages.length === 0)
      newErrors.languages = "At least one language is required";
    else if (formData.languages.length > 3)
      newErrors.languages = "Maximum 3 languages can be selected";

    setErrors(newErrors);

    // If there are errors, scroll to the first error field
    if (Object.keys(newErrors).length > 0) {
      setTimeout(() => {
        scrollToFirstError(newErrors);
      }, 100);
      return false;
    }

    return true;
  };

  // Function to scroll to the first field with an error
  const scrollToFirstError = (errorObj = errors) => {
    const fieldRefs: { [key: string]: React.RefObject<HTMLElement> } = {
      name: nameRef,
      pronouns: pronounsRef,
      yearsOfExperience: yearsOfExperienceRef,
      gender: genderRef,
      designation: designationRef,
      therapyTypes: therapyTypesRef,
      minFee: minFeeRef,
      maxFee: maxFeeRef,
      location: locationRef,
      slotTypes: slotTypesRef,
      qualifications: qualificationsRef,
      values: valuesRef,
      concerns: concernsRef,
      therapyApproach: therapyApproachRef,
      profilePhoto: profilePhotoRef,
      languages: languagesRef
    };

    // Find the first field with an error
    const errorFields = Object.keys(errorObj);
    if (errorFields.length > 0) {
      const firstErrorField = errorFields[0];
      const ref = fieldRefs[firstErrorField];

      if (ref && ref.current) {
        // Force a reflow to ensure DOM is updated
        void ref.current.offsetHeight;

        ref.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add a small delay before focusing to ensure scroll is complete
        setTimeout(() => {
          if (ref.current && (
            ref.current instanceof HTMLInputElement ||
            ref.current instanceof HTMLSelectElement ||
            ref.current instanceof HTMLTextAreaElement
          )) {
            ref.current.focus();
          }
        }, 500);
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!therapistData?._id) {
      return;
    }

    if (validateForm()) {
      try {
        setIsSaving(true);

        // Parse min and max fee values
        const minFee = parseInt(formData.minFee, 10);
        const maxFee = parseInt(formData.maxFee, 10);

        // Prepare data for API in the required format
        const onboardingData: TherapistOnboardingData = {
          name: formData.name,
          pronouns: formData.pronouns,
          yearsOfExperience: parseInt(formData.yearsOfExperience, 10),
          gender: formData.gender,
          designation: formData.designation,
          therapyTypes: formData.therapyTypes,
          minFee: minFee,
          maxFee: maxFee,
          location: formData.location ? [formData.location] : [],
          slotType: formData.slotTypes,
          professionalQualification: formData.qualifications,
          values: formData.values,
          concerns: formData.concerns,
          practiceApproach: formData.therapyApproach,
          languages: formData.languages
        };

        let response;

        // Use different API methods based on whether we're creating or updating
        if (isEditMode) {
          // Cast to unknown first to avoid TypeScript error
          response = await updateTherapistProfile(
            onboardingData,
            formData.profilePhoto,
            therapistData as unknown as import("@/services/public-calendar.service").TherapistData
          );
        } else {
          response = await submitTherapistOnboarding(
            formData.profilePhoto,
            onboardingData
          );
        }

        if (response && response.status === "success") {
          // Set success state
          setSaveSuccess(true);

          // Set edit mode to true after successful creation
          if (!isEditMode) {
            setIsEditMode(true);
          }

          // Mark this tab as completed
          markTabAsCompleted(activeTab);

          // Start countdown for redirection (3 seconds)
          setRedirectCountdown(3);

          // Set up countdown timer
          const countdownInterval = setInterval(() => {
            setRedirectCountdown(prev => {
              if (prev <= 1) {
                clearInterval(countdownInterval);
                setIsSaving(false);

                // Navigate to next tab or page when countdown reaches 0
                const currentTabIndex = tabs.indexOf(activeTab);
                if (currentTabIndex < tabs.length - 1) {
                  // Automatically redirect to the next tab
                  router.push(tabRedirects[tabs[currentTabIndex + 1] as keyof typeof tabRedirects]);
                }
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          // Reset saving state if there was an error
          setIsSaving(false);
        }
      } catch (error) {
        setIsSaving(false);
        console.error("Error updating profile:", error);
      }
    }
  };



  // Calculate progress percentage for the transition loader
  const calculateProgress = () => {
    if (!saveSuccess) return 0;
    return ((3 - redirectCountdown) / 3) * 100;
  };

  return (
    <DashboardLayout>
      {/* Transition loader - shown during saving and redirection */}
      <TransitionLoader
        isVisible={saveSuccess}
        message="Profile saved successfully!"
        redirectMessage="Redirecting to Working Hours..."
        progress={calculateProgress()}
      />
      <div className="p-0">
        {/* White background container with rounded corners */}
        <div className="bg-white rounded-xl shadow-sm">
          {/* Tabs */}
          <div className="mb-8">
            <div className="flex overflow-x-auto no-scrollbar mt-4">
              {tabs.map((tab, index) => {
                // Determine if this tab should be disabled
                const currentTabIndex = tabs.indexOf(activeTab);
                // Check if the tab is completed or is the current tab
                const isCompleted = isTabCompleted(tab);
                const isDisabled = index > currentTabIndex && !isCompleted;

                return (
                  <button
                    key={tab}
                    className={`px-16 py-3 whitespace-nowrap ${
                      activeTab === tab
                        ? "border-b-2 border-yellow-600 text-yellow-600 font-medium"
                        : isDisabled ? "text-gray-300 cursor-not-allowed" : "text-gray-500"
                    }`}
                    onClick={() => {
                      if (!isDisabled) {
                        if (tab === "Setup Onboarding Process") {
                          setActiveTab(tab); // Stay on current page
                        } else {
                          // Navigate to the corresponding page
                          router.push(tabRedirects[tab as keyof typeof tabRedirects]);
                        }
                      }
                    }}
                    disabled={isDisabled}
                  >
                    {tab}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="p-6">

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader size="large" text="Loading profile data..." />
          </div>
        ) : (
          <form className="grid grid-cols-1 md:grid-cols-12 gap-8 relative">

          {/* Mobile Profile Photo - Only visible on small screens */}
          <div className="col-span-1 md:hidden mb-4">
            <label className="block font-medium mb-1">Profile Photo*</label>
            <div className="flex flex-col items-center">
              <div className="rounded-lg border bg-[#F9FBFF] border-gray-300 p-4 flex flex-col w-[285px] h-[290px] items-center">
                <div className={`w-[128px] h-[128px] rounded-full border-2 ${errors.profilePhoto ? 'border-red-500' : 'border-blue-500'} border-dashed flex items-center justify-center mb-4`}>
                  {previewImage ? (
                    <Image
                      src={previewImage}
                      width={200}
                      height={200}
                      alt="Profile Preview"
                      className="w-full h-full object-cover rounded-full"
                      onError={() => {
                        console.error("Error loading image:", previewImage);
                        setPreviewImage(null);
                      }}
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="40"
                        height="40"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-500"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                  )}
                </div>

                <div className="text-center text-xs text-gray-500 mb-4">
                  <p>File types: .jpg, .png</p>
                  <p className="mt-2">Image at least: 200px*200px</p>
                </div>
                {errors.profilePhoto && <p className="text-sm text-red-500 mb-2">{errors.profilePhoto}</p>}

                <input
                  type="file"
                  accept=".jpg,.png"
                  onChange={handleFileChange}
                  id="profilePhotoUploadMobile"
                  style={{ display: "none" }}
                />

                <button
                  type="button"
                  onClick={() => {
                    const fileUpload =
                      document.getElementById("profilePhotoUploadMobile");
                    if (fileUpload) fileUpload.click();
                  }}
                  className="px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-full text-sm font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? "Uploading..." : (previewImage ? "Edit Profile" : "Upload Profile")}
                </button>
              </div>
            </div>
          </div>

          {/* Name */}
          <div className="md:col-span-4">
            <label className="block font-medium mb-1">Name*</label>
            <input
              ref={nameRef}
              type="text"
              name="name"
              placeholder="Enter full name"
              className={`w-full p-3 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
              value={formData.name}
              onChange={handleInputChange}
            />
            {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
          </div>

          {/* Pronouns */}
          <div className="md:col-span-5">
            <label className="block font-medium mb-1">Pronouns*</label>
            <select
              ref={pronounsRef}
              name="pronouns"
              className={`w-full p-3 border ${errors.pronouns ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
              value={formData.pronouns}
              onChange={handleInputChange}
            >
              <option value="">Select pronouns</option>
              {options?.pronounOptions?.map((option: string) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            {errors.pronouns && <p className="text-sm text-red-500 mt-1">{errors.pronouns}</p>}
          </div>

          {/* Desktop Profile Photo - Only visible on medium and larger screens */}
          <div className="md:col-span-3 md:row-span-3 hidden md:block">
            <label className="block font-medium mb-1">Profile Photo*</label>
            <div className="flex flex-col items-center">
              <div ref={profilePhotoRef} className="rounded-lg border bg-[#F9FBFF] border-gray-300 p-4 flex flex-col w-[285px] h-[290px] items-center">
                <div className={`w-[128px] h-[128px] rounded-full border-2 ${errors.profilePhoto ? 'border-red-500' : 'border-blue-500'} border-dashed flex items-center justify-center mb-4`}>
                  {previewImage ? (
                    <Image
                      src={previewImage}
                      width={200}
                      height={200}
                      alt="Profile Preview"
                      className="w-full h-full object-cover rounded-full"
                      onError={() => {
                        console.error("Error loading image:", previewImage);
                        setPreviewImage(null);
                      }}
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="40"
                        height="40"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-500"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                  )}
                </div>

                <div className="text-center text-xs text-gray-500 mb-4">
                  <p>File types: .jpg, .png</p>
                  <p className="mt-2">Image at least: 200px*200px</p>
                </div>
                {errors.profilePhoto && <p className="text-sm text-red-500 mb-2">{errors.profilePhoto}</p>}

                <input
                  type="file"
                  accept=".jpg,.png"
                  onChange={handleFileChange}
                  id="profilePhotoUpload"
                  style={{ display: "none" }}
                />

                <button
                  type="button"
                  onClick={() => {
                    const fileUpload =
                      document.getElementById("profilePhotoUpload");
                    if (fileUpload) fileUpload.click();
                  }}
                  className="px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-full text-sm font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? "Uploading..." : (previewImage ? "Edit Profile" : "Upload Profile")}
                </button>
              </div>
            </div>
          </div>

          {/* Experience, Gender, Age Limit */}
          <div className="col-span-1 md:col-span-9">
            <div className="grid grid-cols-1 md:grid-cols-9 gap-4 mb-4">
              <div className="md:col-span-4">
                <label className="block font-medium mb-1">
                  Years Of Experience*
                </label>
                <input
                  ref={yearsOfExperienceRef}
                  type="text"
                  name="yearsOfExperience"
                  placeholder="e.g., 2 (numbers only)"
                  className={`w-full p-3 border ${errors.yearsOfExperience ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
                  value={formData.yearsOfExperience}
                  onChange={handleInputChange}
                />
                {errors.yearsOfExperience && <p className="text-sm text-red-500 mt-1">{errors.yearsOfExperience}</p>}
              </div>
              <div className="md:col-span-5">
                <div>
                  <label className="block font-medium mb-1">Gender*</label>
                  <select
                    ref={genderRef}
                    name="gender"
                    className={`w-full p-3 border ${errors.gender ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
                    value={formData.gender}
                    onChange={handleInputChange}
                  >
                    <option value="">Select gender</option>
                    {options?.genderOptions?.map((option: string) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  {errors.gender && <p className="text-sm text-red-500 mt-1">{errors.gender}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Designation */}
          <div className="md:col-span-4">
            <label className="block font-medium mb-1">
              Designation (Practicing title)*
            </label>
            <select
              ref={designationRef}
              name="designation"
              className={`w-full p-3 border ${errors.designation ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
              value={formData.designation}
              onChange={handleInputChange}
            >
              <option value="">Select designation</option>
              {options?.designationOptions?.map((option: string) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            {errors.designation && <p className="text-sm text-red-500 mt-1">{errors.designation}</p>}
          </div>

          {/* Type of Therapy */}
          <div className="md:col-span-5">
            <label className="block font-medium mb-1">Type Of Therapy*</label>
            <div ref={therapyTypesRef}>
              <CheckboxMultiSelect
                options={options?.therapyTypeOptions || []}
                selectedValues={formData.therapyTypes}
                onChange={(values) => {
                  setFormData({
                    ...formData,
                    therapyTypes: values,
                  });
                  // Clear error when user selects
                  if (errors.therapyTypes) {
                    setErrors({
                      ...errors,
                      therapyTypes: "",
                    });
                  }
                }}
                placeholder="Select therapy type"
                error={errors.therapyTypes}
              />
            </div>
          </div>

          {/* Languages */}
          <div className="md:col-span-4">
            <div className="w-full">
              <label className="block font-medium mb-1">Languages*</label>
              <div ref={languagesRef}>
                <CheckboxMultiSelect
                  options={options?.languageOptions || []}
                  selectedValues={formData.languages}
                  onChange={(values) => {
                    // Limit to 3 selections
                    const limitedValues = values.slice(0, 3);
                    setFormData({
                      ...formData,
                      languages: limitedValues,
                    });
                    // Clear error when user selects
                    if (errors.languages) {
                      setErrors({
                        ...errors,
                        languages: "",
                      });
                    }
                  }}
                  placeholder="Select language (max 3)"
                  error={errors.languages}
                />
              </div>
            </div>
          </div>

         {/* Fee & Location */}
          <div className="md:col-span-5">
            <div className="w-full">
              <label className="block font-medium mb-1">
                Fee Range (Per session)*
              </label>
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[140px]">
                  <label className="block text-sm text-gray-600 mb-1">Min INR</label>
                  <input
                    ref={minFeeRef}
                    type="text"
                    name="minFee"
                    placeholder="e.g., 2000"
                    className={`w-full p-3 border ${errors.minFee ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
                    value={formData.minFee}
                    onChange={handleInputChange}
                  />
                  {errors.minFee && <p className="text-sm text-red-500 mt-1">{errors.minFee}</p>}
                </div>
                <div className="flex-1 min-w-[140px]">
                  <label className="block text-sm text-gray-600 mb-1">Max INR</label>
                  <input
                    ref={maxFeeRef}
                    type="text"
                    name="maxFee"
                    placeholder="e.g., 3000"
                    className={`w-full p-3 border ${errors.maxFee ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
                    value={formData.maxFee}
                    onChange={handleInputChange}
                  />
                  {errors.maxFee && <p className="text-sm text-red-500 mt-1">{errors.maxFee}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Slot Type */}
          <div className="md:col-span-7">
            <label className="block font-medium mb-1">Slot Type (Min)*</label>
            <div ref={slotTypesRef}>
              <CheckboxMultiSelect
                options={options?.slotTypeOptions || []}
                selectedValues={formData.slotTypes}
                onChange={(values) => {
                  setFormData({
                    ...formData,
                    slotTypes: values,
                  });
                  // Clear error when user selects
                  if (errors.slotTypes) {
                    setErrors({
                      ...errors,
                      slotTypes: "",
                    });
                  }
                }}
                placeholder="Select slot type"
                error={errors.slotTypes}
              />
            </div>
          </div>

          {/* Location */}
          <div className="md:col-span-2 pl-2">
              <label className="block font-medium mb-1">Location*</label>
              <select
                ref={locationRef}
                name="location"
                className={`w-full p-3 border ${errors.location ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
                value={formData.location}
                onChange={handleInputChange}
              >
                <option value="">Select location</option>
                {options?.locationOptions?.map((option: string) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              {errors.location && <p className="text-sm text-red-500 mt-1">{errors.location}</p>}
            </div>

          {/* Qualifications */}
          <div className="col-span-1 md:col-span-9">
            <label className="block font-medium mb-1">
              Professional Qualifications & Trainings*
            </label>
            <textarea
              ref={qualificationsRef}
              rows={3}
              name="qualifications"
              placeholder="e.g., M.A In Counselling Psychology, Tata Institute Of Social Sciences"
              className={`w-full p-3 border ${errors.qualifications ? 'border-red-500' : 'border-gray-300'} rounded-lg ${
                formData.qualifications === "M.A in Counselling Psychology, Tata Institute of Social Sciences ; MPhil in Clinical Psychology, National Institute of Mental Health and Sciences"
                  ? 'text-gray-400 italic'
                  : 'text-primary'
              }`}
              value={formData.qualifications}
              onChange={handleInputChange}
            />
            {errors.qualifications && <p className="text-sm text-red-500 mt-1">{errors.qualifications}</p>}
          </div>

          {/* Therapist Values */}
          <div className="col-span-1 md:col-span-9">
            <label className="block font-medium mb-1">
              Values I Align With as a Therapist*
            </label>
            <div ref={valuesRef}>
              <CheckboxMultiSelect
                options={options?.valueOptions || []}
                selectedValues={formData.values}
                onChange={(values) => {
                  // Limit to 3 selections
                  const limitedValues = values.slice(0, 3);
                  setFormData({
                    ...formData,
                    values: limitedValues,
                  });
                  // Clear error when user selects
                  if (errors.values) {
                    setErrors({
                      ...errors,
                      values: "",
                    });
                  }
                }}
                placeholder="Select values (max 3)"
                error={errors.values}
              />
            </div>
          </div>

          {/* Concerns */}
          <div className="col-span-1 md:col-span-9">
            <label className="block font-medium mb-1">
              <p>{"Concerns I'm Comfortable Working With*"}</p>
            </label>
            <div ref={concernsRef}>
              <CheckboxMultiSelect
                options={options?.concernOptions || []}
                selectedValues={formData.concerns}
                onChange={(values) => {
                  setFormData({
                    ...formData,
                    concerns: values,
                  });
                  // Clear error when user selects
                  if (errors.concerns) {
                    setErrors({
                      ...errors,
                      concerns: "",
                    });
                  }
                }}
                placeholder="Select concerns"
                error={errors.concerns}
              />
            </div>
          </div>

          {/* What therapy looks like with me */}
          <div className="col-span-1 md:col-span-9">
            <label className="block font-medium mb-1">What therapy looks like with me*</label>
            <textarea
              ref={therapyApproachRef}
              rows={4}
              name="therapyApproach"
              placeholder="Describe what therapy looks like with you..."
              className={`w-full p-3 border ${errors.therapyApproach ? 'border-red-500' : 'border-gray-300'} rounded-lg ${
                formData.therapyApproach === "In our sessions, we'll first spend some time understanding what's bringing you to therapy and what you hope to get out of it. Then, we'll slowly explore the deeper reasons behind those concerns.\nIn therapy, understanding yourself better is powerful—and it's often the first step toward real change."
                  ? 'text-gray-400 italic'
                  : 'text-primary'
              }`}
              value={formData.therapyApproach}
              onChange={handleInputChange}
              maxLength={600}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.therapyApproach && <p className="text-sm text-red-500">{errors.therapyApproach}</p>}
              <p className="text-sm text-gray-500 ml-auto">
                {formData.therapyApproach.length}/600 characters
              </p>
            </div>
          </div>

            {/* Continue Button */}
            <div className="col-span-1 md:col-span-12 text-start flex gap-4">
              <AnimatedButton
                onClick={handleSubmit}
                isLoading={isSaving}
                isSuccess={saveSuccess}
                disabled={isSaving || saveSuccess}
                loadingText="Saving..."
                successText="Redirecting..."
                className="w-[250px] h-[48px]"
              >
                Continue
              </AnimatedButton>
            </div>
        </form>
        )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default PublicCalendar;

