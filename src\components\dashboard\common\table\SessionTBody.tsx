import {
  GoogleMeetIcon,
  RegularBinIcon,
  RegularNotificationIcon,
} from "../../../../../public/assets/Svgs";
import React from "react";
import { formatDate, formatTime } from "@/utils/axios";
import Image from "next/image";
import Link from "next/link";
import { WarningCircle } from "@phosphor-icons/react";

interface SessionTBodyProps {
  TableData: Array<unknown>; // Define the type of TableData here
  sessionLoading: boolean;
  setIsUpdatePayment?: (value: boolean) => void;
  isUpdatePayment?: boolean;
  setIsRescheduleSession?: (value: boolean) => void;
  isRescheduleSession?: boolean;
  setIsReminderModal?: (value: boolean) => void;
  isReminderModal?: boolean;
  setIsCanceledSessionModal?: (value: boolean) => void;
  isCanceledSessionModal?: boolean;
  setSingleSessionID?: (value: string) => void;
  setPaymentID?: (value: string) => void;
  setRescheduleSessionID?: (value: string) => void;
  setSingleSessionData: (value: Item) => void;
  therapistData?: {
    minFee?: number;
    maxFee?: number;
  };
}

export interface Item {
  img?: string;
  clientId?: {
    name?: string;
    clientId?: string;
  };
  meetLink?: string;
  email?: string;
  amount: string | number;
  time?: string;
  status?: string;
  date?: string;
  fromDate: string;
  toDate: string;
  tillDate: string;
  recurrenceDates?: {
    _id: string;
  };
  _id: string;
  fromPublicCalender?: boolean;
  publicCalendarAmountUpdated?: boolean;
}

const SessionTBody: React.FC<SessionTBodyProps> = ({
  TableData,
  setIsUpdatePayment,
  // isUpdatePayment,
  setIsRescheduleSession,
  // isRescheduleSession,
  setIsReminderModal,
  isReminderModal,
  setIsCanceledSessionModal,
  isCanceledSessionModal,
  sessionLoading,
  setSingleSessionID,
  setPaymentID,
  setRescheduleSessionID,
  setSingleSessionData,
  therapistData,
}) => {
  const skeletonRows = Array(5).fill(0); // Adjust the number to match the desired skeleton rows

  return (
    <tbody className="divide-y divide-primary/10">
      {sessionLoading ? (
        skeletonRows.map((_, index) => (
          <tr key={index} className="animate-pulse">
            <td className="p-15px">
              <div className="flex items-center gap-3">
                <div className="w-[34px] h-[34px] rounded-full bg-gray-200"></div>
                <div className="space-y-2">
                  <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  <div className="h-3 w-40 bg-gray-200 rounded"></div>
                </div>
              </div>
            </td>
            <td className="p-15px">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 w-36 bg-gray-200 rounded"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 w-20 bg-gray-200 rounded"></div>
            </td>
            <td className="p-15px">
              <div className="h-6 w-24 bg-gray-200 rounded-full"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
            </td>
            <td className="p-15px">
              <div className="flex gap-5">
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
              </div>
            </td>
          </tr>
        ))
      ) : TableData?.length === 0 ? (
        // Display a "Data is not found" message if TableData is empty and not loading
        <tr>
          <td
            colSpan={8}
            className="p-15px text-center text-primary/70 text-sm/5"
          >
            <div className="flex flex-col items-center justify-center pt-16 pb-20">
              <Image
                width={1000}
                height={1000}
                src="/assets/images/dashboard/not-found-session-list.webp"
                alt="no-data"
                className="w-[185px] h-auto"
              />
              <p className="text-xl/6 font-semibold pt-4 text-primary">
                No Sessions Found!
              </p>
            </div>
          </td>
        </tr>
      ) : (
        (TableData as Item[])?.map((item, index) => {
          return (
            <tr
              key={index}
              className={`${item.fromPublicCalender ? 'bg-orange-50 group' : ''}`}
            >
              <td className="p-15px relative">
                {item.fromPublicCalender && (
                  <div className="absolute left-0 -top-10 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-in-out pointer-events-none z-50">
                    <div className="bg-white border border-gray-200 shadow-lg rounded-md px-3 py-3 text-xs text-gray-700 w-40 h-16 flex items-center justify-center text-center leading-relaxed relative">
                      Session created by client through public calendar
                      {/* White arrow pointing down to the row */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                        <div className="w-0 h-0 border-l-6 border-r-6 border-t-8 border-l-transparent border-r-transparent border-t-white"></div>
                      </div>
                    </div>
                  </div>
                )}
                <div className="flex items-center gap-3">
                  <div className="w-[34px] h-[34px] rounded-full border border-primary/20 bg-[#F5F5F7] overflow-hidden flex items-center justify-center">
                    <span className="text-xs_18 text-[#72748D] font-medium uppercase">
                      {item?.clientId?.name &&
                        item?.clientId?.name
                          .split(" ")
                          .map((n) => n.charAt(0))
                          .join("")}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm/5 font-semibold text-primary flex items-center gap-1.5 capitalize">
                      {item?.clientId?.name}{" "}
                      {item?.meetLink &&
                        ["rescheduled", "confirmed"].includes(
                          (item.status ?? "").trim()
                        ) && (
                          <Link
                            href={item?.meetLink}
                            target="_blank"
                            className="w-3.5 h-auto"
                          >
                            <GoogleMeetIcon />
                          </Link>
                        )}
                      <span className="relative text-primary cursor-pointer group/client-id">
                        <WarningCircle size={16} />

                        <span className="absolute top-0 left-0 invisible group-hover/client-id:visible sm:whitespace-nowrap whitespace-normal w-64 sm:w-auto inline-block px-2 py-1 bg-primary/80 text-white text-xs z-10 rounded">
                          {item?.clientId?.clientId}
                        </span>
                      </span>
                    </p>
                    <p className="text-xs_18 text-primary/70 font-medium">
                      {item.email}
                    </p>
                  </div>
                </div>
              </td>
              <td className="p-15px text-primary/70 text-sm/5 font-medium whitespace-nowrap">
                {formatDate(item.fromDate)} - {formatDate(item.toDate)}
              </td>
              <td className="p-15px text-primary/70 text-sm/5 font-medium uppercase whitespace-nowrap">
                {formatTime(item.fromDate)} - {formatTime(item.toDate)}
              </td>
              <td className="p-15px text-primary text-sm/5 font-semibold">
                {item.fromPublicCalender && (item.amount === "0" || item.amount === 0)
                  ? "Introductory call"
                  : item.fromPublicCalender && therapistData?.minFee && therapistData?.maxFee &&
                    !item.publicCalendarAmountUpdated
                    ? `₹ ${therapistData.minFee} - ₹ ${therapistData.maxFee}`
                    : `₹ ${item.amount}`
                }
              </td>
              <td className="p-15px">
                <span
                  className={`inline-block py-1.5 px-3 font-semibold  rounded-full text-xs capitalize ${
                    item.status && item.status.trim() === "completed"
                      ? "bg-green-200 text-green-500"
                      : item.status &&
                        ["Upcoming", "confirmed"].includes(item.status.trim())
                      ? "bg-orange-200 text-orange-600"
                      : item.status && item.status.trim() === "cancelled"
                      ? "bg-[#FFEDED] text-[#FF5959]"
                      : item.status && item.status.trim() === "rescheduled"
                      ? "bg-yellow-600/10 text-yellow-600"
                      : ""
                  }`}
                >
                  {item.status}
                </span>
              </td>
              <td className="p-15px">
                <div className="flex items-center gap-6 text-sm/6 font-medium">
                  {/* <button
                    onClick={() => {
                      if (
                        !["completed", "cancelled"].includes(
                          item.status?.trim() ?? ""
                        )
                      ) {
                        setIsRescheduleSession?.(true);
                        setRescheduleSessionID?.(
                          item?.recurrenceDates?._id || ""
                        );
                        setSingleSessionData?.(item); // Store the entire session data
                      }
                    }}
                    className={`underline ${
                      ["completed", "cancelled"].includes(
                        item.status?.trim() ?? ""
                      )
                        ? "text-green-600/50 cursor-not-allowed"
                        : "text-green-600"
                    }`}
                  >
                    Reschedule
                  </button> */}

                  <button
                    onClick={() => {

                      setIsRescheduleSession?.(true);
                      setRescheduleSessionID?.(
                        item?.recurrenceDates?._id || ""
                      );
                      setSingleSessionData?.(item);
                    }}
                    className={`underline text-green-600 `}
                  >
                    Reschedule
                  </button>

                  <button
                    onClick={() => {
                      if (
                        !["completed", "cancelled"].includes(
                          item.status?.trim() ?? ""
                        )
                      ) {
                        setIsUpdatePayment?.(true);
                        setPaymentID?.(item?.recurrenceDates?._id || ""); // Store the session ID
                      }
                    }}
                    className={`underline hidden ${
                      ["completed", "cancelled"].includes(
                        item.status?.trim() ?? ""
                      )
                        ? "text-green-600/50 cursor-not-allowed"
                        : "text-green-600"
                    }`}
                  >
                    Update Payment
                  </button>
                </div>
              </td>
              <td className="p-15px">
                {item.status &&
                ["completed", "cancelled"].includes(item.status.trim()) ? (
                  <div className="flex items-center gap-5">
                    <span>-</span>
                    <span>-</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-5">
                    <RegularNotificationIcon
                      className="w-5 h-5 cursor-pointer"
                      pathFillColor="#242424"
                      strokeWidth={`1.5`}
                      onClick={() => {
                        setIsReminderModal?.(!isReminderModal);
                        setSingleSessionData?.(item);
                        if (item?.recurrenceDates?._id) {
                          setSingleSessionID?.(item?.recurrenceDates?._id);
                        }
                      }}
                    />
                    <RegularBinIcon
                      className="w-5 h-5 cursor-pointer"
                      strokeWidth={`1.5`}
                      onClick={() => {
                        setIsCanceledSessionModal?.(!isCanceledSessionModal);
                        if (item?.recurrenceDates?._id) {
                          setSingleSessionID?.(item?.recurrenceDates?._id);
                        }
                      }}
                    />
                  </div>
                )}
              </td>
            </tr>
          );
        })
      )}
    </tbody>
  );
};

export default SessionTBody;